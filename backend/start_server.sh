#!/bin/bash

# AI Contact Filtering Backend Startup Script
# This script starts the Python FastAPI backend server with Pydantic AI

echo "🚀 Starting AI Contact Filtering Backend..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run setup.sh first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit backend/.env with your API keys"
fi

# Activate virtual environment and start server
echo "🔧 Activating virtual environment..."
source venv/bin/activate

echo "🌐 Starting FastAPI server on http://localhost:8000..."
echo "📚 API Documentation available at http://localhost:8000/docs"
echo "🔍 Health check: http://localhost:8000/api/health"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
