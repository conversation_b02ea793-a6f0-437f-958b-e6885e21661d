"""
Pydantic AI-powered General Chat Agent with Context7 integration
"""
from typing import List, Optional
from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from models.types import AIConfig, ConversationMessage
from services.context7_service import Context7Service

class ChatResponse(BaseModel):
    """Response from chat agent"""
    response: str
    is_contact_query: bool = False

class GeneralChatAgent:
    """AI-powered general chat agent using Pydantic AI"""

    def __init__(self):
        self.ai_config: Optional[AIConfig] = None
        self.context7_service = Context7Service()

        # Store system prompts as strings
        self.chat_system_prompt = """
            You are a helpful AI assistant for a contact filtering application.
            You can help users with general questions about the application,
            provide guidance on how to use features, and answer questions about
            contact management and filtering.

            If a user asks about searching for specific contacts or filtering
            contacts based on criteria, politely redirect them to use the
            contact filtering feature instead.

            Be helpful, concise, and professional in your responses.
            """

        self.detector_system_prompt = """
            Determine if the user's query is asking to search for, filter, or find
            specific contacts based on criteria like company, job title, location, etc.

            Return True if it's a contact search/filtering query.
            Return False if it's a general question or conversation.

            Examples of contact queries:
            - "Find contacts at Loblaws"
            - "Show me managers in Toronto"
            - "Who works in the grocery industry?"
            - "Search for buyers"

            Examples of general queries:
            - "How does this app work?"
            - "What features are available?"
            - "Hello, how are you?"
            - "Can you help me understand the interface?"
            """

        # Initialize Pydantic AI agent for general chat
        self.chat_agent = Agent(
            'openai:gpt-4o',
            output_type=str,
            system_prompt=self.chat_system_prompt
        )

        # Agent for detecting contact queries
        self.contact_query_detector = Agent(
            'openai:gpt-4o',
            output_type=bool,
            system_prompt=self.detector_system_prompt
        )

    def set_ai_config(self, config: AIConfig):
        """Set AI configuration for the agent"""
        self.ai_config = config

        # Map frontend provider names to Pydantic AI model names
        provider_mapping = {
            'openai': 'openai',
            'anthropic': 'anthropic',
            'gemini': 'gemini',
            'openrouter': 'openai',  # OpenRouter uses OpenAI-compatible API
            'local': 'openai'  # Fallback to OpenAI format for local models
        }

        provider = provider_mapping.get(config.provider, 'openai')
        model_name = f"{provider}:{config.model}"

        # Set up environment variables for API keys
        import os
        if config.provider == 'openai' and config.api_key:
            os.environ['OPENAI_API_KEY'] = config.api_key
        elif config.provider == 'anthropic' and config.api_key:
            os.environ['ANTHROPIC_API_KEY'] = config.api_key
        elif config.provider == 'gemini' and config.api_key:
            os.environ['GOOGLE_API_KEY'] = config.api_key
        elif config.provider == 'openrouter' and config.api_key:
            os.environ['OPENAI_API_KEY'] = config.api_key
            # Set base URL for OpenRouter
            if config.base_url:
                os.environ['OPENAI_BASE_URL'] = config.base_url

        # Update the agent models based on config
        try:
            self.chat_agent = Agent(
                model_name,
                output_type=str,
                system_prompt=self.chat_system_prompt
            )

            self.contact_query_detector = Agent(
                model_name,
                output_type=bool,
                system_prompt=self.detector_system_prompt
            )
        except Exception as e:
            print(f"Failed to update agent models to {model_name}: {e}")
            # Keep the default agents if update fails

    def is_contact_query(self, query: str) -> bool:
        """Determine if the query is asking for contact filtering"""
        try:
            # Use simple heuristics first for speed
            contact_keywords = [
                'find', 'search', 'show', 'list', 'contacts', 'people',
                'company', 'manager', 'director', 'buyer', 'specialist',
                'toronto', 'vancouver', 'calgary', 'montreal', 'alberta',
                'loblaws', 'metro', 'sobeys', 'walmart', 'costco'
            ]

            query_lower = query.lower()
            if any(keyword in query_lower for keyword in contact_keywords):
                return True

            return False

        except Exception as e:
            print(f"Error detecting contact query: {e}")
            return False

    async def is_contact_query_ai(self, query: str) -> bool:
        """Use AI to determine if the query is asking for contact filtering"""
        try:
            result = await self.contact_query_detector.run(query)
            return result.output
        except Exception as e:
            print(f"AI contact query detection failed: {e}")
            return self.is_contact_query(query)

    async def process_general_chat(
        self,
        query: str,
        conversation_history: List[ConversationMessage] = None
    ) -> str:
        """
        Process a general chat query using Pydantic AI
        """
        try:
            # Use Context7 to enhance responses with up-to-date documentation
            await self._enhance_with_context7(query)

            # Build context from conversation history
            context = ""
            if conversation_history:
                context = "\n".join([
                    f"{msg.role}: {msg.content}"
                    for msg in conversation_history[-5:]  # Last 5 messages
                ])
                context = f"Previous conversation:\n{context}\n\nCurrent query: {query}"
            else:
                context = query

            # Generate response using Pydantic AI
            result = await self.chat_agent.run(context)
            return result.output

        except Exception as e:
            print(f"Error processing general chat: {e}")
            return self._fallback_response(query)

    async def _enhance_with_context7(self, query: str):
        """Use Context7 to enhance responses with up-to-date documentation"""
        try:
            # Check if query is about specific technologies or libraries
            tech_keywords = {
                'pydantic': 'pydantic-ai',
                'fastapi': 'fastapi',
                'ai': 'pydantic-ai',
                'agent': 'pydantic-ai',
                'api': 'fastapi'
            }

            query_lower = query.lower()
            for keyword, library in tech_keywords.items():
                if keyword in query_lower:
                    docs = await self.context7_service.get_library_docs(library)
                    print(f"Enhanced with Context7 docs for {library}: {len(docs)} characters")
                    break

        except Exception as e:
            print(f"Context7 enhancement failed: {e}")

    def _fallback_response(self, query: str) -> str:
        """Fallback response when AI fails"""
        if any(word in query.lower() for word in ['help', 'how', 'what', 'guide']):
            return """
            I'm here to help! This is a contact filtering application that allows you to:

            • Search and filter contacts using natural language queries
            • Find contacts by company, job title, location, or industry
            • Use AI-powered matching to find relevant contacts

            To search for contacts, try queries like:
            - "Find managers at Loblaws"
            - "Show me contacts in Toronto"
            - "Who works in the grocery industry?"

            Is there something specific you'd like to know about the application?
            """

        return "I'm here to help! Could you please rephrase your question or let me know what you'd like to know about the contact filtering application?"
