"""
FastAPI backend for AI Contact Filtering System using Pydantic AI
"""
import os
from typing import List, Optional, Dict, Any
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv

from agents.contact_filter_agent import ContactFilterAgent
from agents.general_chat_agent import GeneralChatAgent
from models.types import (
    Contact,
    MatchResult,
    AIConfig,
    ExtractedEntities,
    ProcessQueryRequest,
    ProcessQueryResponse,
    GeneralChatRequest,
    GeneralChatResponse
)

# Load environment variables
load_dotenv()

app = FastAPI(
    title="AI Contact Filtering API",
    description="Pydantic AI-powered contact filtering with Context7 integration",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global agent instances
contact_filter_agent = ContactFilterAgent()
general_chat_agent = GeneralChatAgent()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "AI Contact Filtering API is running", "status": "healthy"}

@app.post("/api/agents/contact-filter/process", response_model=ProcessQueryResponse)
async def process_contact_query(request: ProcessQueryRequest):
    """
    Process a contact filtering query using Pydantic AI agent
    """
    try:
        # Set AI configuration if provided
        if request.ai_config:
            contact_filter_agent.set_ai_config(request.ai_config)

        # Process the query
        results = await contact_filter_agent.process_query(
            query=request.query,
            contacts=request.contacts
        )

        return ProcessQueryResponse(
            results=results,
            query=request.query,
            total_matches=len(results)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.post("/api/agents/general-chat/process", response_model=GeneralChatResponse)
async def process_general_chat(request: GeneralChatRequest):
    """
    Process a general chat query using Pydantic AI agent
    """
    try:
        # Set AI configuration if provided
        if request.ai_config:
            general_chat_agent.set_ai_config(request.ai_config)

        # Check if it's a contact query
        is_contact_query = general_chat_agent.is_contact_query(request.query)

        if is_contact_query:
            return GeneralChatResponse(
                response="This appears to be a contact search query. Please use the contact filtering feature instead.",
                is_contact_query=True
            )

        # Process general chat
        response = await general_chat_agent.process_general_chat(
            query=request.query,
            conversation_history=request.conversation_history or []
        )

        return GeneralChatResponse(
            response=response,
            is_contact_query=False
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing chat: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "agents": {
            "contact_filter": "ready",
            "general_chat": "ready"
        },
        "context7": "integrated"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
