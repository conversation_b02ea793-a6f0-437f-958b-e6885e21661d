# AI-Powered Contact Filtering System

A comprehensive contact management application that uses AI to intelligently filter messy contact data through natural language queries. Built with **Pydantic AI**, **FastAPI**, **Next.js**, and **Context7** for up-to-date documentation integration.

## Features

### 🤖 AI-Powered Filtering
- **Natural Language Queries**: Ask questions like "Find all category managers from grocery chains in Western Canada"
- **Pydantic AI Agents**: Structured AI agents for query parsing, entity extraction, and intelligent matching
- **Context7 Integration**: Real-time access to up-to-date library documentation for enhanced AI responses

### 📊 Intelligent Data Processing
- **Multi-Strategy Matching**: Exact, fuzzy, phonetic, and semantic similarity matching
- **Entity Extraction**: Automatically identifies companies, job titles, locations, and industries
- **Confidence Scoring**: Results ranked by relevance with explanations

### 📁 Data Management
- **CSV Upload**: Support for multiple file uploads with automatic header detection
- **Data Cleaning**: Intelligent normalization and handling of messy data
- **File Management**: Preview, merge, and manage multiple data sources

### 📈 Analytics & Insights
- **Performance Metrics**: Query success rates, response times, and user satisfaction
- **Data Composition**: Industry, location, and company breakdowns
- **Usage Statistics**: Query history and filtering patterns

### 🎯 Advanced Features
- **Bulk Operations**: Select and export filtered results
- **Multiple Export Formats**: CSV, JSON, and Excel support
- **Real-time Processing**: Non-blocking data processing with progress indicators
- **Responsive Design**: Modern UI with Tailwind CSS

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: FastAPI, Python 3.12+
- **AI/ML**: Pydantic AI, Context7
- **AI Providers**: OpenAI, Anthropic Claude, OpenRouter, Google Gemini
- **Styling**: Tailwind CSS, Lucide React icons
- **Data Processing**: Papa Parse (CSV), Fuse.js (fuzzy search)
- **Matching Algorithms**: String similarity, Metaphone, custom location intelligence

## Getting Started

### Prerequisites
- Node.js 18+
- Python 3.8+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd csv-query-app
```

2. Set up the Python backend:
```bash
cd backend
./setup.sh
```

3. Configure environment variables:
```bash
# Edit backend/.env with your API keys
cp backend/.env.example backend/.env
```

4. Install frontend dependencies:
```bash
cd ..
npm install
```

5. Set up frontend environment:
```bash
cp .env.example .env.local
```

### Running the Application

1. Start the Python backend:
```bash
cd backend
./start_server.sh
```

2. In a new terminal, start the frontend:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

The backend API will be available at [http://localhost:8000](http://localhost:8000).

## Usage

### 1. Upload Contact Data
- Navigate to the **Data** tab
- Drag and drop CSV files or click to select
- Preview uploaded data and manage files

### 2. AI-Powered Filtering
- Go to the **Chat** tab
- Ask natural language questions:
  - "Find bakeries in Alberta"
  - "Show me category managers from Superstore"
  - "List all contacts in Western Canada"

### 3. View and Export Results
- Check the **Results** tab for detailed filtering results
- Select contacts and export in various formats
- View match confidence scores and explanations

### 4. Analytics
- Monitor performance in the **Analytics** tab
- View data composition and usage statistics
- Track query history and success rates

### 5. AI Provider Configuration
- Go to the **Settings** tab to configure AI providers
- **Frontend Configuration**: All AI settings are configured in the frontend UI
- **OpenAI**: Use your OpenAI API key with models like GPT-4, GPT-4 Turbo
- **Anthropic**: Configure Claude 3 models (Opus, Sonnet, Haiku)
- **OpenRouter**: Access 100+ models with a single API key
  - Get API key from [OpenRouter Keys](https://openrouter.ai/keys)
  - Use model IDs like `openai/gpt-4`, `anthropic/claude-3-sonnet`
- **Google Gemini**: Use Gemini Pro and Gemini 1.5 models
  - Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Dynamic Configuration**: API keys and models are passed to the Python backend dynamically
- Test connections and adjust matching thresholds

## Architecture

### Pydantic AI Agent Workflow
1. **Query Parsing**: Analyze natural language input using Pydantic AI
2. **Entity Extraction**: Structured extraction of companies, roles, locations, industries
3. **Context7 Enhancement**: Real-time documentation lookup for enhanced responses
4. **Fuzzy Matching**: Apply multiple matching algorithms
5. **Result Ranking**: Sort by relevance and confidence

### Backend API Endpoints
- `POST /api/agents/contact-filter/process`: Process contact filtering queries
- `POST /api/agents/general-chat/process`: Handle general chat interactions
- `GET /api/health`: Health check and system status

### Frontend-Backend Communication
- **Dynamic AI Configuration**: Frontend passes API keys and model settings to backend
- **Structured Responses**: Pydantic models ensure type-safe API communication
- **Fallback Handling**: Local processing when backend is unavailable

## Development

### Project Structure
```
├── backend/            # Python FastAPI backend
│   ├── agents/         # Pydantic AI agents
│   │   ├── contact_filter_agent.py
│   │   └── general_chat_agent.py
│   ├── models/         # Pydantic models
│   ├── services/       # Context7 and other services
│   ├── utils/          # Utility functions
│   ├── main.py         # FastAPI application
│   ├── requirements.txt
│   ├── setup.sh        # Backend setup script
│   └── start_server.sh # Server startup script
├── src/                # Next.js frontend
│   ├── app/            # Next.js app router
│   ├── components/     # React components
│   │   ├── ui/         # Reusable UI components
│   │   ├── ChatInterface.tsx
│   │   ├── DataManagement.tsx
│   │   ├── ResultsView.tsx
│   │   ├── SettingsPanel.tsx
│   │   └── AnalyticsView.tsx
│   ├── agents/         # Frontend agent interfaces
│   ├── lib/            # Utility functions
│   │   ├── matching.ts # Matching algorithms
│   │   └── aiProviders.ts # Backend API client
│   └── types/          # TypeScript definitions
```

### Key Components
- **ContactFilterAgent**: Pydantic AI agent for query processing
- **GeneralChatAgent**: Pydantic AI agent for general conversations
- **Context7Service**: Real-time documentation integration
- **Backend API Client**: Type-safe frontend-backend communication
- **Matching Library**: Multi-strategy text matching algorithms

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please open an issue on GitHub or contact the development team.
